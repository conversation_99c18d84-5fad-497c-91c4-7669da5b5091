{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx"], "sourcesContent": ["'use client';\nimport { cn } from '@/lib/utils';\nimport { useMotionValue, animate, motion } from 'motion/react';\nimport { useState, useEffect } from 'react';\nimport useMeasure from 'react-use-measure';\n\nexport type InfiniteSliderProps = {\n  children: React.ReactNode;\n  gap?: number;\n  speed?: number;\n  speedOnHover?: number;\n  direction?: 'horizontal' | 'vertical';\n  reverse?: boolean;\n  className?: string;\n};\n\nexport function InfiniteSlider({\n  children,\n  gap = 16,\n  speed = 100,\n  speedOnHover,\n  direction = 'horizontal',\n  reverse = false,\n  className,\n}: InfiniteSliderProps) {\n  const [currentSpeed, setCurrentSpeed] = useState(speed);\n  const [ref, { width, height }] = useMeasure();\n  const translation = useMotionValue(0);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [key, setKey] = useState(0);\n\n  useEffect(() => {\n    let controls;\n    const size = direction === 'horizontal' ? width : height;\n    const contentSize = size + gap;\n    const from = reverse ? -contentSize / 2 : 0;\n    const to = reverse ? 0 : -contentSize / 2;\n\n    const distanceToTravel = Math.abs(to - from);\n    const duration = distanceToTravel / currentSpeed;\n\n    if (isTransitioning) {\n      const remainingDistance = Math.abs(translation.get() - to);\n      const transitionDuration = remainingDistance / currentSpeed;\n\n      controls = animate(translation, [translation.get(), to], {\n        ease: 'linear',\n        duration: transitionDuration,\n        onComplete: () => {\n          setIsTransitioning(false);\n          setKey((prevKey) => prevKey + 1);\n        },\n      });\n    } else {\n      controls = animate(translation, [from, to], {\n        ease: 'linear',\n        duration: duration,\n        repeat: Infinity,\n        repeatType: 'loop',\n        repeatDelay: 0,\n        onRepeat: () => {\n          translation.set(from);\n        },\n      });\n    }\n\n    return controls?.stop;\n  }, [\n    key,\n    translation,\n    currentSpeed,\n    width,\n    height,\n    gap,\n    isTransitioning,\n    direction,\n    reverse,\n  ]);\n\n  const hoverProps = speedOnHover\n    ? {\n        onHoverStart: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speedOnHover);\n        },\n        onHoverEnd: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speed);\n        },\n      }\n    : {};\n\n  return (\n    <div className={cn('overflow-hidden', className)}>\n      <motion.div\n        className='flex w-max'\n        style={{\n          ...(direction === 'horizontal'\n            ? { x: translation }\n            : { y: translation }),\n          gap: `${gap}px`,\n          flexDirection: direction === 'horizontal' ? 'row' : 'column',\n        }}\n        ref={ref}\n        {...hoverProps}\n      >\n        {children}\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;;AAgBO,SAAS,eAAe,KAQT;QARS,EAC7B,QAAQ,EACR,MAAM,EAAE,EACR,QAAQ,GAAG,EACX,YAAY,EACZ,YAAY,YAAY,EACxB,UAAU,KAAK,EACf,SAAS,EACW,GARS;;IAS7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,wSAAQ,EAAC;IACjD,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,GAAG,IAAA,+QAAU;IAC3C,MAAM,cAAc,IAAA,6SAAc,EAAC;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,wSAAQ,EAAC;IACvD,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,wSAAQ,EAAC;IAE/B,IAAA,ySAAS;oCAAC;YACR,IAAI;YACJ,MAAM,OAAO,cAAc,eAAe,QAAQ;YAClD,MAAM,cAAc,OAAO;YAC3B,MAAM,OAAO,UAAU,CAAC,cAAc,IAAI;YAC1C,MAAM,KAAK,UAAU,IAAI,CAAC,cAAc;YAExC,MAAM,mBAAmB,KAAK,GAAG,CAAC,KAAK;YACvC,MAAM,WAAW,mBAAmB;YAEpC,IAAI,iBAAiB;gBACnB,MAAM,oBAAoB,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;gBACvD,MAAM,qBAAqB,oBAAoB;gBAE/C,WAAW,IAAA,oSAAO,EAAC,aAAa;oBAAC,YAAY,GAAG;oBAAI;iBAAG,EAAE;oBACvD,MAAM;oBACN,UAAU;oBACV,UAAU;oDAAE;4BACV,mBAAmB;4BACnB;4DAAO,CAAC,UAAY,UAAU;;wBAChC;;gBACF;YACF,OAAO;gBACL,WAAW,IAAA,oSAAO,EAAC,aAAa;oBAAC;oBAAM;iBAAG,EAAE;oBAC1C,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,QAAQ;oDAAE;4BACR,YAAY,GAAG,CAAC;wBAClB;;gBACF;YACF;YAEA,OAAO,qBAAA,+BAAA,SAAU,IAAI;QACvB;mCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,eACf;QACE,cAAc;YACZ,mBAAmB;YACnB,gBAAgB;QAClB;QACA,YAAY;YACV,mBAAmB;YACnB,gBAAgB;QAClB;IACF,IACA,CAAC;IAEL,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,mBAAmB;kBACpC,cAAA,4TAAC,6SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,GAAI,cAAc,eACd;oBAAE,GAAG;gBAAY,IACjB;oBAAE,GAAG;gBAAY,CAAC;gBACtB,KAAK,AAAC,GAAM,OAAJ,KAAI;gBACZ,eAAe,cAAc,eAAe,QAAQ;YACtD;YACA,KAAK;YACJ,GAAG,UAAU;;gBAEb;gBACA;;;;;;;;;;;;AAIT;GA/FgB;;QAUmB,+QAAU;QACvB,6SAAc;;;KAXpB", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx"], "sourcesContent": ["'use client';\nimport { cn } from '@/lib/utils';\nimport { HTMLMotionProps, motion } from 'motion/react';\n\nexport const GRADIENT_ANGLES = {\n  top: 0,\n  right: 90,\n  bottom: 180,\n  left: 270,\n};\n\nexport type ProgressiveBlurProps = {\n  direction?: keyof typeof GRADIENT_ANGLES;\n  blurLayers?: number;\n  className?: string;\n  blurIntensity?: number;\n} & HTMLMotionProps<'div'>;\n\nexport function ProgressiveBlur({\n  direction = 'bottom',\n  blurLayers = 8,\n  className,\n  blurIntensity = 0.25,\n  ...props\n}: ProgressiveBlurProps) {\n  const layers = Math.max(blurLayers, 2);\n  const segmentSize = 1 / (blurLayers + 1);\n\n  return (\n    <div className={cn('relative', className)}>\n      {Array.from({ length: layers }).map((_, index) => {\n        const angle = GRADIENT_ANGLES[direction];\n        const gradientStops = [\n          index * segmentSize,\n          (index + 1) * segmentSize,\n          (index + 2) * segmentSize,\n          (index + 3) * segmentSize,\n        ].map(\n          (pos, posIndex) =>\n            `rgba(255, 255, 255, ${posIndex === 1 || posIndex === 2 ? 1 : 0}) ${pos * 100}%`\n        );\n\n        const gradient = `linear-gradient(${angle}deg, ${gradientStops.join(\n          ', '\n        )})`;\n\n        return (\n          <motion.div\n            key={index}\n            className='pointer-events-none absolute inset-0 rounded-[inherit]'\n            style={{\n              maskImage: gradient,\n              WebkitMaskImage: gradient,\n              backdropFilter: `blur(${index * blurIntensity}px)`,\n              WebkitBackdropFilter: `blur(${index * blurIntensity}px)`,\n            }}\n            {...props}\n          />\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAFA;;;;AAIO,MAAM,kBAAkB;IAC7B,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AASO,SAAS,gBAAgB,KAMT;QANS,EAC9B,YAAY,QAAQ,EACpB,aAAa,CAAC,EACd,SAAS,EACT,gBAAgB,IAAI,EACpB,GAAG,OACkB,GANS;IAO9B,MAAM,SAAS,KAAK,GAAG,CAAC,YAAY;IACpC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;IAEvC,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,YAAY;kBAC5B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAO,GAAG,GAAG,CAAC,CAAC,GAAG;YACtC,MAAM,QAAQ,eAAe,CAAC,UAAU;YACxC,MAAM,gBAAgB;gBACpB,QAAQ;gBACR,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;aACf,CAAC,GAAG,CACH,CAAC,KAAK,WACJ,AAAC,uBAAmE,OAA7C,aAAa,KAAK,aAAa,IAAI,IAAI,GAAE,MAAc,OAAV,MAAM,KAAI;YAGlF,MAAM,WAAW,AAAC,mBAA+B,OAAb,OAAM,SAExC,OAF+C,cAAc,IAAI,CACjE,OACA;YAEF,qBACE,4TAAC,6SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,iBAAiB;oBACjB,gBAAgB,AAAC,QAA6B,OAAtB,QAAQ,eAAc;oBAC9C,sBAAsB,AAAC,QAA6B,OAAtB,QAAQ,eAAc;gBACtD;gBACC,GAAG,KAAK;eARJ;;;;;QAWX;;;;;;AAGN;KA5CgB", "debugId": null}}]}