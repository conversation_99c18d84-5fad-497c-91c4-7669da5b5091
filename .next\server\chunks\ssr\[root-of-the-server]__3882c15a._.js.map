{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,kPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,6SAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as NavigationMenuPrimitive from '@radix-ui/react-navigation-menu';\nimport { cva } from 'class-variance-authority';\nimport { ChevronDownIcon } from 'lucide-react';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean;\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      className={cn(\n        'group/navigation-menu relative flex max-w-max flex-1 items-center justify-center',\n        className\n      )}\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  );\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      className={cn(\n        'group flex flex-1 list-none items-center justify-center gap-1',\n        className\n      )}\n      data-slot=\"navigation-menu-list\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      className={cn('relative', className)}\n      data-slot=\"navigation-menu-item\"\n      {...props}\n    />\n  );\n}\n\nconst navigationMenuTriggerStyle = cva(\n  'group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 font-medium text-sm outline-none transition-[color,box-shadow] hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=open]:bg-accent/50 data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:hover:bg-accent'\n);\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      className={cn(navigationMenuTriggerStyle(), 'group', className)}\n      data-slot=\"navigation-menu-trigger\"\n      {...props}\n    >\n      {children}{' '}\n      <ChevronDownIcon\n        aria-hidden=\"true\"\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  );\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      className={cn(\n        'data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out md:absolute md:w-auto',\n        'group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 **:data-[slot=navigation-menu-link]:focus:outline-none **:data-[slot=navigation-menu-link]:focus:ring-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in',\n        className\n      )}\n      data-slot=\"navigation-menu-content\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        'absolute top-full left-0 isolate z-50 flex justify-center'\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        className={cn(\n          'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full origin-top-center overflow-hidden rounded-2xl border bg-popover text-popover-foreground shadow data-[state=closed]:animate-out data-[state=open]:animate-in md:w-[var(--radix-navigation-menu-viewport-width)]',\n          className\n        )}\n        data-slot=\"navigation-menu-viewport\"\n        {...props}\n      />\n    </div>\n  );\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      className={cn(\n        \"flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent [&_svg:not([class*='size-'])]:size-4 [&_svg:not([class*='text-'])]:text-muted-foreground\",\n        className\n      )}\n      data-slot=\"navigation-menu-link\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      className={cn(\n        'data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=hidden]:animate-out data-[state=visible]:animate-in',\n        className\n      )}\n      data-slot=\"navigation-menu-indicator\"\n      {...props}\n    >\n      <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  );\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAGA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,6WAAC,6RAA4B;QAC3B,WAAW,IAAA,kHAAE,EACX,oFACA;QAEF,aAAU;QACV,iBAAe;QACd,GAAG,KAAK;;YAER;YACA,0BAAY,6WAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6WAAC,6RAA4B;QAC3B,WAAW,IAAA,kHAAE,EACX,iEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6WAAC,6RAA4B;QAC3B,WAAW,IAAA,kHAAE,EAAC,YAAY;QAC1B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,IAAA,kPAAG,EACpC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6WAAC,gSAA+B;QAC9B,WAAW,IAAA,kHAAE,EAAC,8BAA8B,SAAS;QACrD,aAAU;QACT,GAAG,KAAK;;YAER;YAAU;0BACX,6WAAC,+TAAe;gBACd,eAAY;gBACZ,WAAU;;;;;;;;;;;;AAIlB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6WAAC,gSAA+B;QAC9B,WAAW,IAAA,kHAAE,EACX,oWACA,6hCACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX;kBAGF,cAAA,6WAAC,iSAAgC;YAC/B,WAAW,IAAA,kHAAE,EACX,uVACA;YAEF,aAAU;YACT,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6WAAC,6RAA4B;QAC3B,WAAW,IAAA,kHAAE,EACX,ydACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,6WAAC,kSAAiC;QAChC,WAAW,IAAA,kHAAE,EACX,gMACA;QAEF,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,6WAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/config/docs.ts"], "sourcesContent": ["import {\r\n  ChartNoAxesCombinedIcon,\r\n  CircleCheckIcon,\r\n  DatabaseZapIcon,\r\n  HouseIcon,\r\n  ImageIcon,\r\n  MessageCircleIcon,\r\n  NotebookPenIcon,\r\n  PenLineIcon,\r\n  UsersIcon,\r\n} from \"lucide-react\";\r\nimport type { TFeature, TFooterLink, TNavItem } from \"@/types\";\r\n\r\nexport const navItems: TNavItem[] = [\r\n  {\r\n    href: \"/pricing\",\r\n    label: \"Pricing\",\r\n    icon: HouseIcon,\r\n  },\r\n  {\r\n    href: \"/about\",\r\n    label: \"About\",\r\n    icon: NotebookPenIcon,\r\n  },\r\n  {\r\n    href: \"/docs\",\r\n    label: \"Docs\",\r\n    icon: UsersIcon,\r\n  },\r\n  {\r\n    href: \"/privacy\",\r\n    label: \"Privacy\",\r\n    icon: ImageIcon,\r\n  },\r\n];\r\n\r\nexport const footerLinks: TFooterLink[] = [\r\n  {\r\n    title: \"Newsroom\",\r\n    links: [\r\n      { name: \"Latest News\", href: \"/\", external: false },\r\n      { name: \"Top Stories\", href: \"/\", external: false },\r\n      { name: \"Editor's Picks\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Company\",\r\n    links: [\r\n      { name: \"About Us\", href: \"/\", external: false },\r\n      { name: \"Careers\", href: \"/\", external: false },\r\n      { name: \"Press\", href: \"/\", external: false },\r\n      { name: \"Contact\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"For Business\",\r\n    links: [\r\n      { name: \"Advertise with Us\", href: \"/\", external: false },\r\n      { name: \"Media Kit\", href: \"/\", external: false },\r\n      { name: \"Partner with Us\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"More\",\r\n    links: [\r\n      { name: \"Newsletter\", href: \"/\", external: false },\r\n      { name: \"Mobile App\", href: \"/\", external: false },\r\n      { name: \"RSS Feeds\", href: \"/\", external: false },\r\n      { name: \"Help Center\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Terms & Policies\",\r\n    links: [\r\n      { name: \"Terms of Use\", href: \"/\", external: false },\r\n      { name: \"Privacy Policy\", href: \"/\", external: false },\r\n      { name: \"Cookie Policy\", href: \"/\", external: false },\r\n      { name: \"Editorial Policy\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Safety\",\r\n    links: [\r\n      { name: \"Fact-Checking\", href: \"/\", external: false },\r\n      { name: \"Corrections\", href: \"/\", external: false },\r\n      { name: \"Trust & Transparency\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Follow Us\",\r\n    links: [\r\n      { name: \"Facebook\", href: \"/\", external: true },\r\n      { name: \"Twitter\", href: \"/\", external: true },\r\n      { name: \"Instagram\", href: \"/\", external: true },\r\n      { name: \"YouTube\", href: \"/\", external: true },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Sections\",\r\n    links: [\r\n      { name: \"Politics\", href: \"/\", external: false },\r\n      { name: \"Business\", href: \"/\", external: false },\r\n      { name: \"Technology\", href: \"/\", external: false },\r\n      { name: \"Health\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Resources\",\r\n    links: [\r\n      { name: \"Media Resources\", href: \"/\", external: false },\r\n      { name: \"Author Guidelines\", href: \"/\", external: false },\r\n      { name: \"News Archive\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Community\",\r\n    links: [\r\n      { name: \"Events\", href: \"/\", external: false },\r\n      { name: \"Reader Stories\", href: \"/\", external: false },\r\n      { name: \"Submit News\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n];\r\n\r\nexport const features: TFeature[] = [\r\n  {\r\n    icon: MessageCircleIcon,\r\n    title: \"chat\",\r\n    description: \"Chat with anyone in team.\",\r\n  },\r\n  {\r\n    icon: PenLineIcon,\r\n    title: \"writing\",\r\n    description: \"Notion like editor for writing.\",\r\n  },\r\n  {\r\n    icon: CircleCheckIcon,\r\n    title: \"tasks\",\r\n    description: \"Automated task tracking.\",\r\n  },\r\n  {\r\n    icon: UsersIcon,\r\n    title: \"teams\",\r\n    description: \"Collaborate with your team.\",\r\n  },\r\n  {\r\n    icon: DatabaseZapIcon,\r\n    title: \"storage\",\r\n    description: \"Unlimited storage for your files.\",\r\n  },\r\n  {\r\n    icon: ChartNoAxesCombinedIcon,\r\n    title: \"analytics\",\r\n    description: \"Easy to track your progress.\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAaO,MAAM,WAAuB;IAClC;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,+TAAe;IACvB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;CACD;AAEM,MAAM,cAA6B;IACxC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;YAC9C;gBAAE,MAAM;gBAAS,MAAM;gBAAK,UAAU;YAAM;YAC5C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;SACvD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;YACnD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAK,UAAU;YAAM;SACxD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAwB,MAAM;gBAAK,UAAU;YAAM;SAC5D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAK;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;YACtD;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;SACpD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;YAC7C;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;CACD;AAEM,MAAM,WAAuB;IAClC;QACE,MAAM,qUAAiB;QACvB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mTAAW;QACjB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,ySAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+VAAuB;QAC7B,OAAO;QACP,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/main-nav.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  NavigationMenu,\r\n  NavigationMenuContent,\r\n  NavigationMenuItem,\r\n  NavigationMenuLink,\r\n  NavigationMenuList,\r\n  NavigationMenuTrigger,\r\n} from '@/components/ui/navigation-menu';\r\nimport { features, navItems } from '@/config/docs';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <nav className={cn('items-center gap-0.5', className)} {...props}>\r\n      <NavigationMenu>\r\n        <NavigationMenuList>\r\n          <NavigationMenuItem className=\"rounded-2xl\">\r\n            <NavigationMenuTrigger\r\n              aria-label=\"Open blog links\"\r\n              className=\"flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50\"\r\n            >\r\n              Features\r\n            </NavigationMenuTrigger>\r\n            <NavigationMenuContent className=\"dark bg-my-background/40 backdrop-blur-lg supports-backdrop-blur:bg-my-background/90\">\r\n              <div className=\"grid w-[500px] p-4 lg:w-[600px]\">\r\n                <p className=\"font-medium text-muted-foreground capitalize tracking-tighter\">\r\n                  Features\r\n                </p>\r\n                <div className=\"grid grid-cols-2 gap-6 py-6\">\r\n                  {features.map((feature) => (\r\n                    <NavigationMenuLink\r\n                      asChild\r\n                      className=\"group rounded-xl p-0 hover:bg-transparent\"\r\n                      key={feature.title}\r\n                    >\r\n                      <Link href=\"/\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <div className=\"rounded-lg bg-muted p-3 transition-all duration-300 group-hover:bg-brand-500 dark:group-hover:bg-brand-500\">\r\n                            <feature.icon className=\"block size-5 transition-all duration-300 group-hover:text-white dark:group-hover:text-black\" />\r\n                          </div>\r\n\r\n                          <div className=\"flex flex-col gap-1\">\r\n                            <div className=\"font-medium text-md leading-none\">\r\n                              {feature.title}\r\n                            </div>\r\n                            <p className=\"text-muted-foreground text-sm\">\r\n                              {feature.description}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </Link>\r\n                    </NavigationMenuLink>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </NavigationMenuContent>\r\n          </NavigationMenuItem>\r\n        </NavigationMenuList>\r\n      </NavigationMenu>\r\n      {navItems.map((item) => (\r\n        <Button\r\n          asChild\r\n          className=\"rounded-full\"\r\n          key={item.href}\r\n          size=\"sm\"\r\n          variant=\"ghost\"\r\n        >\r\n          <Link\r\n            className={cn(\r\n              'font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary',\r\n              pathname === item.href && 'text-primary'\r\n            )}\r\n            href={item.href}\r\n          >\r\n            {item.label}\r\n          </Link>\r\n        </Button>\r\n      ))}\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AAdA;;;;;;;;AAgBO,SAAS,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,MAAM,WAAW,IAAA,gRAAW;IAE5B,qBACE,6WAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,wBAAwB;QAAa,GAAG,KAAK;;0BAC9D,6WAAC,yJAAc;0BACb,cAAA,6WAAC,6JAAkB;8BACjB,cAAA,6WAAC,6JAAkB;wBAAC,WAAU;;0CAC5B,6WAAC,gKAAqB;gCACpB,cAAW;gCACX,WAAU;0CACX;;;;;;0CAGD,6WAAC,gKAAqB;gCAAC,WAAU;0CAC/B,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAE,WAAU;sDAAgE;;;;;;sDAG7E,6WAAC;4CAAI,WAAU;sDACZ,0HAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,6WAAC,6JAAkB;oDACjB,OAAO;oDACP,WAAU;8DAGV,cAAA,6WAAC,sSAAI;wDAAC,MAAK;kEACT,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;;;;;;8EAG1B,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAI,WAAU;sFACZ,QAAQ,KAAK;;;;;;sFAEhB,6WAAC;4EAAE,WAAU;sFACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;mDAbvB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0BjC,0HAAQ,CAAC,GAAG,CAAC,CAAC,qBACb,6WAAC,qIAAM;oBACL,OAAO;oBACP,WAAU;oBAEV,MAAK;oBACL,SAAQ;8BAER,cAAA,6WAAC,sSAAI;wBACH,WAAW,IAAA,kHAAE,EACX,sFACA,aAAa,KAAK,IAAI,IAAI;wBAE5B,MAAM,KAAK,IAAI;kCAEd,KAAK,KAAK;;;;;;mBAXR,KAAK,IAAI;;;;;;;;;;;AAiBxB", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-header.tsx"], "sourcesContent": ["'use client';\r\nimport { ArmchairIcon } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { useEffect, useState } from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '../ui/button';\r\nimport { MainNav } from './main-nav';\r\n\r\nexport default function SiteHeader() {\r\n  const [showBg, setShowBg] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setShowBg(window.scrollY > 160);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n  return (\r\n    <header className=\"fixed top-4 right-0 left-0 z-50 flex w-full translate-y-0 justify-center opacity-100 transition-all duration-500 ease-out\">\r\n      <nav\r\n        className={cn(\r\n          'relative mx-2 flex h-[60px] w-full max-w-6xl select-none items-center justify-between rounded-full bg-transparent px-1.5 py-2 transition-all duration-200 ease-in-out sm:mx-4 xl:grid xl:grid-cols-3 xl:gap-8',\r\n          showBg &&\r\n            'bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90'\r\n        )}\r\n      >\r\n        <div className=\"flex items-center gap-4 xl:justify-start\">\r\n          <div className=\"flex translate-y-0 items-center gap-1.5 px-3 opacity-100 transition-all delay-75 duration-500 ease-out\">\r\n            <Link className=\"flex items-center gap-1.5\" href=\"/\">\r\n              <ArmchairIcon className=\"block size-6 text-brand-600\" />\r\n              <span className=\"pb-[1.5px] font-medium text-lg\">\r\n                Better Flow\r\n              </span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <MainNav className=\"hidden lg:flex\" />\r\n        <div className=\"flex items-center justify-end gap-2 xl:justify-end\">\r\n          <Button className=\"rounded-full bg-brand-500 hover:bg-brand-400\">\r\n            Book Demo\r\n          </Button>\r\n        </div>\r\n      </nav>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,gVAAQ,EAAC;IAErC,IAAA,iVAAS,EAAC;QACR,MAAM,eAAe;YACnB,UAAU,OAAO,OAAO,GAAG;QAC7B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IACL,qBACE,6WAAC;QAAO,WAAU;kBAChB,cAAA,6WAAC;YACC,WAAW,IAAA,kHAAE,EACX,iNACA,UACE;;8BAGJ,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,sSAAI;4BAAC,WAAU;4BAA4B,MAAK;;8CAC/C,6WAAC,kTAAY;oCAAC,WAAU;;;;;;8CACxB,6WAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;8BAMvD,6WAAC,+IAAO;oBAAC,WAAU;;;;;;8BACnB,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC,qIAAM;wBAAC,WAAU;kCAA+C;;;;;;;;;;;;;;;;;;;;;;AAO3E", "debugId": null}}]}