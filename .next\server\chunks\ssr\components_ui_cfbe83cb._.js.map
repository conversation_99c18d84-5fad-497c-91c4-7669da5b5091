{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx"], "sourcesContent": ["'use client';\nimport { cn } from '@/lib/utils';\nimport { useMotionValue, animate, motion } from 'motion/react';\nimport { useState, useEffect } from 'react';\nimport useMeasure from 'react-use-measure';\n\nexport type InfiniteSliderProps = {\n  children: React.ReactNode;\n  gap?: number;\n  speed?: number;\n  speedOnHover?: number;\n  direction?: 'horizontal' | 'vertical';\n  reverse?: boolean;\n  className?: string;\n};\n\nexport function InfiniteSlider({\n  children,\n  gap = 16,\n  speed = 100,\n  speedOnHover,\n  direction = 'horizontal',\n  reverse = false,\n  className,\n}: InfiniteSliderProps) {\n  const [currentSpeed, setCurrentSpeed] = useState(speed);\n  const [ref, { width, height }] = useMeasure();\n  const translation = useMotionValue(0);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [key, setKey] = useState(0);\n\n  useEffect(() => {\n    let controls;\n    const size = direction === 'horizontal' ? width : height;\n    const contentSize = size + gap;\n    const from = reverse ? -contentSize / 2 : 0;\n    const to = reverse ? 0 : -contentSize / 2;\n\n    const distanceToTravel = Math.abs(to - from);\n    const duration = distanceToTravel / currentSpeed;\n\n    if (isTransitioning) {\n      const remainingDistance = Math.abs(translation.get() - to);\n      const transitionDuration = remainingDistance / currentSpeed;\n\n      controls = animate(translation, [translation.get(), to], {\n        ease: 'linear',\n        duration: transitionDuration,\n        onComplete: () => {\n          setIsTransitioning(false);\n          setKey((prevKey) => prevKey + 1);\n        },\n      });\n    } else {\n      controls = animate(translation, [from, to], {\n        ease: 'linear',\n        duration: duration,\n        repeat: Infinity,\n        repeatType: 'loop',\n        repeatDelay: 0,\n        onRepeat: () => {\n          translation.set(from);\n        },\n      });\n    }\n\n    return controls?.stop;\n  }, [\n    key,\n    translation,\n    currentSpeed,\n    width,\n    height,\n    gap,\n    isTransitioning,\n    direction,\n    reverse,\n  ]);\n\n  const hoverProps = speedOnHover\n    ? {\n        onHoverStart: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speedOnHover);\n        },\n        onHoverEnd: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speed);\n        },\n      }\n    : {};\n\n  return (\n    <div className={cn('overflow-hidden', className)}>\n      <motion.div\n        className='flex w-max'\n        style={{\n          ...(direction === 'horizontal'\n            ? { x: translation }\n            : { y: translation }),\n          gap: `${gap}px`,\n          flexDirection: direction === 'horizontal' ? 'row' : 'column',\n        }}\n        ref={ref}\n        {...hoverProps}\n      >\n        {children}\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAgBO,SAAS,eAAe,EAC7B,QAAQ,EACR,MAAM,EAAE,EACR,QAAQ,GAAG,EACX,YAAY,EACZ,YAAY,YAAY,EACxB,UAAU,KAAK,EACf,SAAS,EACW;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,gVAAQ,EAAC;IACjD,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,GAAG,IAAA,4QAAU;IAC3C,MAAM,cAAc,IAAA,0SAAc,EAAC;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,gVAAQ,EAAC;IACvD,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gVAAQ,EAAC;IAE/B,IAAA,iVAAS,EAAC;QACR,IAAI;QACJ,MAAM,OAAO,cAAc,eAAe,QAAQ;QAClD,MAAM,cAAc,OAAO;QAC3B,MAAM,OAAO,UAAU,CAAC,cAAc,IAAI;QAC1C,MAAM,KAAK,UAAU,IAAI,CAAC,cAAc;QAExC,MAAM,mBAAmB,KAAK,GAAG,CAAC,KAAK;QACvC,MAAM,WAAW,mBAAmB;QAEpC,IAAI,iBAAiB;YACnB,MAAM,oBAAoB,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;YACvD,MAAM,qBAAqB,oBAAoB;YAE/C,WAAW,IAAA,iSAAO,EAAC,aAAa;gBAAC,YAAY,GAAG;gBAAI;aAAG,EAAE;gBACvD,MAAM;gBACN,UAAU;gBACV,YAAY;oBACV,mBAAmB;oBACnB,OAAO,CAAC,UAAY,UAAU;gBAChC;YACF;QACF,OAAO;YACL,WAAW,IAAA,iSAAO,EAAC,aAAa;gBAAC;gBAAM;aAAG,EAAE;gBAC1C,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR,YAAY,GAAG,CAAC;gBAClB;YACF;QACF;QAEA,OAAO,UAAU;IACnB,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,eACf;QACE,cAAc;YACZ,mBAAmB;YACnB,gBAAgB;QAClB;QACA,YAAY;YACV,mBAAmB;YACnB,gBAAgB;QAClB;IACF,IACA,CAAC;IAEL,qBACE,6WAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,mBAAmB;kBACpC,cAAA,6WAAC,0SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,GAAI,cAAc,eACd;oBAAE,GAAG;gBAAY,IACjB;oBAAE,GAAG;gBAAY,CAAC;gBACtB,KAAK,GAAG,IAAI,EAAE,CAAC;gBACf,eAAe,cAAc,eAAe,QAAQ;YACtD;YACA,KAAK;YACJ,GAAG,UAAU;;gBAEb;gBACA;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx"], "sourcesContent": ["'use client';\nimport { cn } from '@/lib/utils';\nimport { HTMLMotionProps, motion } from 'motion/react';\n\nexport const GRADIENT_ANGLES = {\n  top: 0,\n  right: 90,\n  bottom: 180,\n  left: 270,\n};\n\nexport type ProgressiveBlurProps = {\n  direction?: keyof typeof GRADIENT_ANGLES;\n  blurLayers?: number;\n  className?: string;\n  blurIntensity?: number;\n} & HTMLMotionProps<'div'>;\n\nexport function ProgressiveBlur({\n  direction = 'bottom',\n  blurLayers = 8,\n  className,\n  blurIntensity = 0.25,\n  ...props\n}: ProgressiveBlurProps) {\n  const layers = Math.max(blurLayers, 2);\n  const segmentSize = 1 / (blurLayers + 1);\n\n  return (\n    <div className={cn('relative', className)}>\n      {Array.from({ length: layers }).map((_, index) => {\n        const angle = GRADIENT_ANGLES[direction];\n        const gradientStops = [\n          index * segmentSize,\n          (index + 1) * segmentSize,\n          (index + 2) * segmentSize,\n          (index + 3) * segmentSize,\n        ].map(\n          (pos, posIndex) =>\n            `rgba(255, 255, 255, ${posIndex === 1 || posIndex === 2 ? 1 : 0}) ${pos * 100}%`\n        );\n\n        const gradient = `linear-gradient(${angle}deg, ${gradientStops.join(\n          ', '\n        )})`;\n\n        return (\n          <motion.div\n            key={index}\n            className='pointer-events-none absolute inset-0 rounded-[inherit]'\n            style={{\n              maskImage: gradient,\n              WebkitMaskImage: gradient,\n              backdropFilter: `blur(${index * blurIntensity}px)`,\n              WebkitBackdropFilter: `blur(${index * blurIntensity}px)`,\n            }}\n            {...props}\n          />\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAFA;;;;AAIO,MAAM,kBAAkB;IAC7B,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AASO,SAAS,gBAAgB,EAC9B,YAAY,QAAQ,EACpB,aAAa,CAAC,EACd,SAAS,EACT,gBAAgB,IAAI,EACpB,GAAG,OACkB;IACrB,MAAM,SAAS,KAAK,GAAG,CAAC,YAAY;IACpC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;IAEvC,qBACE,6WAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,YAAY;kBAC5B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAO,GAAG,GAAG,CAAC,CAAC,GAAG;YACtC,MAAM,QAAQ,eAAe,CAAC,UAAU;YACxC,MAAM,gBAAgB;gBACpB,QAAQ;gBACR,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;aACf,CAAC,GAAG,CACH,CAAC,KAAK,WACJ,CAAC,oBAAoB,EAAE,aAAa,KAAK,aAAa,IAAI,IAAI,EAAE,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC;YAGpF,MAAM,WAAW,CAAC,gBAAgB,EAAE,MAAM,KAAK,EAAE,cAAc,IAAI,CACjE,MACA,CAAC,CAAC;YAEJ,qBACE,6WAAC,0SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,iBAAiB;oBACjB,gBAAgB,CAAC,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC;oBAClD,sBAAsB,CAAC,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC;gBAC1D;gBACC,GAAG,KAAK;eARJ;;;;;QAWX;;;;;;AAGN", "debugId": null}}]}