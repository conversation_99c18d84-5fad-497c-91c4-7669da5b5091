{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,kPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,6SAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const InfiniteSlider = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfiniteSlider() from the server but InfiniteSlider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/infinite-slider.tsx <module evaluation>\",\n    \"InfiniteSlider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,uYAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const InfiniteSlider = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfiniteSlider() from the server but InfiniteSlider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/infinite-slider.tsx\",\n    \"InfiniteSlider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,uYAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GRADIENT_ANGLES = registerClientReference(\n    function() { throw new Error(\"Attempted to call G<PERSON><PERSON>ENT_ANGLES() from the server but <PERSON><PERSON><PERSON>ENT_ANGLES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx <module evaluation>\",\n    \"GRADIENT_ANGLES\",\n);\nexport const ProgressiveBlur = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressiveBlur() from the server but ProgressiveBlur is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx <module evaluation>\",\n    \"ProgressiveBlur\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA;AAEG,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GRADIENT_ANGLES = registerClientReference(\n    function() { throw new Error(\"Attempted to call G<PERSON><PERSON>ENT_ANGLES() from the server but <PERSON><PERSON><PERSON>ENT_ANGLES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx\",\n    \"GRADIENT_ANGLES\",\n);\nexport const ProgressiveBlur = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressiveBlur() from the server but ProgressiveBlur is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx\",\n    \"ProgressiveBlur\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA;AAEG,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["/** biome-ignore-all lint/performance/noImgElement: <explanation> */\r\nimport { InfiniteSlider } from '@/components/ui/infinite-slider';\r\nimport { ProgressiveBlur } from '@/components/ui/progressive-blur';\r\n\r\nconst logos = [\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/nvidia.svg',\r\n    alt: 'Nvidia Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/column.svg',\r\n    alt: 'Column Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/github.svg',\r\n    alt: 'GitHub Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/nike.svg',\r\n    alt: 'Nike Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/lemonsqueezy.svg',\r\n    alt: 'Lemon Squeezy Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/laravel.svg',\r\n    alt: 'Laravel Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/lilly.svg',\r\n    alt: 'Lilly Logo',\r\n  },\r\n  {\r\n    src: 'https://html.tailus.io/blocks/customers/openai.svg',\r\n    alt: 'OpenAI Logo',\r\n  },\r\n];\r\nexport default function LogoCloud() {\r\n  return (\r\n    <section className=\"overflow-hidden bg-background py-10\">\r\n      <div className=\"group relative m-auto max-w-7xl\">\r\n        <div className=\"relative\">\r\n          <InfiniteSlider gap={112} speed={40} speedOnHover={20}>\r\n            {logos.map((logo) => (\r\n              <div className=\"flex\" key={logo.alt}>\r\n                <img\r\n                  alt={logo.alt}\r\n                  className=\"mx-auto h-5 w-fit invert-75\"\r\n                  height=\"20\"\r\n                  src={logo.src}\r\n                  width=\"auto\"\r\n                />\r\n              </div>\r\n            ))}\r\n          </InfiniteSlider>\r\n\r\n          <div className=\"absolute inset-y-0 left-0 w-20 bg-linear-to-r from-background\" />\r\n          <div className=\"absolute inset-y-0 right-0 w-20 bg-linear-to-l from-background\" />\r\n          <ProgressiveBlur\r\n            blurIntensity={1}\r\n            className=\"pointer-events-none absolute top-0 left-0 h-full w-20\"\r\n            direction=\"left\"\r\n          />\r\n          <ProgressiveBlur\r\n            blurIntensity={1}\r\n            className=\"pointer-events-none absolute top-0 right-0 h-full w-20\"\r\n            direction=\"right\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAClE;AACA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;CACD;AACc,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yJAAc;wBAAC,KAAK;wBAAK,OAAO;wBAAI,cAAc;kCAChD,MAAM,GAAG,CAAC,CAAC,qBACV,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCACC,KAAK,KAAK,GAAG;oCACb,WAAU;oCACV,QAAO;oCACP,KAAK,KAAK,GAAG;oCACb,OAAM;;;;;;+BANiB,KAAK,GAAG;;;;;;;;;;kCAYvC,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC,2JAAe;wBACd,eAAe;wBACf,WAAU;wBACV,WAAU;;;;;;kCAEZ,6WAAC,2JAAe;wBACd,eAAe;wBACf,WAAU;wBACV,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import { Button } from '@/components/ui/button';\r\nimport LogoCloud from './logo-cloud';\r\n\r\nexport default function HeroHome() {\r\n  return (\r\n    <section\r\n      aria-labelledby=\"hero-section\"\r\n      className=\"relative mt-10 mb-[160px] flex h-full flex-col justify-center sm:h-full lg:mt-20\"\r\n    >\r\n      <div className=\"grid grid-cols-1 items-center gap-8 py-8 sm:py-0 lg:grid-cols-1\">\r\n        <div className=\"flex flex-col items-center justify-center py-8 text-center sm:min-h-[280px] lg:text-left\">\r\n          <div className=\"flex w-full flex-col\">\r\n            <h1 className=\"flex flex-col items-center\">\r\n              <div className=\"mx-auto inline-block max-w-[440px] text-pretty break-words text-center font-aeonik font-medium text-4xl text-white/80 tracking-none md:max-w-[600px] md:text-5xl md:leading-[1.05em] lg:max-w-[780px] lg:text-[65px]\">\r\n                The AI Assistant that works everywhere you do.\r\n              </div>\r\n            </h1>\r\n          </div>\r\n          <p className=\"mt-4 max-w-2xl whitespace-pre-line text-center font-aeonik font-normal text-lg text-muted-foreground sm:mt-8 md:text-xl\">\r\n            Highlight keeps track of your meetings, chats, and tasks so you can\r\n            find answers, create, and act, all in one place, personalized for\r\n            you.\r\n          </p>\r\n          <div className=\"mt-6 flex items-center gap-6 lg:mt-8 xl:mt-10\">\r\n            <Button\r\n              className=\"rounded-full bg-brand-500 hover:bg-brand-400\"\r\n              size={'lg'}\r\n            >\r\n              Book Demo\r\n            </Button>\r\n            <Button\r\n              className=\"rounded-full bg-brand-500 hover:bg-brand-400\"\r\n              size={'lg'}\r\n            >\r\n              Learn more\r\n            </Button>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-col gap-12 md:flex-col lg:gap-16\">\r\n          {/* companies we work with */}\r\n          <section className=\"flex flex-col items-center gap-4\">\r\n            <div className=\"flex flex-col items-center font-aeonik\">\r\n              <h2 className=\"max-w-[200px] text-center font-medium text-md text-muted-foreground/50 sm:max-w-none sm:text-lg\">\r\n                Loved by 100,000+ users and teams worldwide!\r\n              </h2>\r\n            </div>\r\n            <LogoCloud />\r\n          </section>\r\n          {/* hero image */}\r\n          <section className=\"aspect-[1/1] w-full sm:aspect-[16/9] md:p-8\">\r\n            <div className=\"relative h-full w-full overflow-hidden rounded-[20px] bg-[#0D0D0D] md:rounded-[30px]\">\r\n              <video\r\n                aria-label=\"Demo video 1\"\r\n                className=\"absolute inset-0 h-full w-full object-cover\"\r\n                loop=\"\"\r\n                playsinline=\"\"\r\n                role=\"presentation\"\r\n                src=\"https://cdn.highlightai.com/media/landing/misc/hero_demo.webm\"\r\n                style=\"opacity: 1;\"\r\n              />\r\n            </div>\r\n          </section>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QACC,mBAAgB;QAChB,WAAU;kBAEV,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAG,WAAU;0CACZ,cAAA,6WAAC;oCAAI,WAAU;8CAAuN;;;;;;;;;;;;;;;;sCAK1O,6WAAC;4BAAE,WAAU;sCAA0H;;;;;;sCAKvI,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,qIAAM;oCACL,WAAU;oCACV,MAAM;8CACP;;;;;;8CAGD,6WAAC,qIAAM;oCACL,WAAU;oCACV,MAAM;8CACP;;;;;;;;;;;;;;;;;;8BAKL,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAQ,WAAU;;8CACjB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAG,WAAU;kDAAkG;;;;;;;;;;;8CAIlH,6WAAC,6IAAS;;;;;;;;;;;sCAGZ,6WAAC;4BAAQ,WAAU;sCACjB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCACC,cAAW;oCACX,WAAU;oCACV,MAAK;oCACL,aAAY;oCACZ,MAAK;oCACL,KAAI;oCACJ,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/page.tsx"], "sourcesContent": ["import HeroHome from '@/features/home/<USER>';\n\nexport default function Home() {\n  return (\n    <main className=\"pt-[90px]\">\n      <HeroHome />\n      <section className=\"mb-40\">\n        <div className=\"mx-auto flex max-w-6xl flex-col gap-6 px-0 py-20 md:px-10 lg:py-48 2xl:max-w-7xl\">\n          <div className=\"font-sans\">\n            <h2 className=\"font-bold\">Sans</h2>\n            <p>\n              Are you looking for inspiration to create a stunning portfolio\n              website that stands out in the given landing page examples?\n              Crafting a captivating online portfolio can be an intimidating\n              task, especially when you're trying to find the right template.\n              There is a way to simplify the process and find a NextJS portfolio\n              template tailored to your needs. Imagine having a solution that\n              allows you to jumpstart your digital presence and showcase your\n              skills seamlessly.\n            </p>\n          </div>\n          <div className=\"font-mono\">\n            <h2 className=\"font-bold\">Mono</h2>\n            <p>\n              Are you looking for inspiration to create a stunning portfolio\n              website that stands out in the given landing page examples?\n              Crafting a captivating online portfolio can be an intimidating\n              task, especially when you're trying to find the right template.\n              There is a way to simplify the process and find a NextJS portfolio\n              template tailored to your needs. Imagine having a solution that\n              allows you to jumpstart your digital presence and showcase your\n              skills seamlessly.\n            </p>\n          </div>\n          <div className=\"font-aeonik\">\n            <h2 className=\"font-bold\">Aeonik</h2>\n            <p>\n              Are you looking for inspiration to create a stunning portfolio\n              website that stands out in the given landing page examples?\n              Crafting a captivating online portfolio can be an intimidating\n              task, especially when you're trying to find the right template.\n              There is a way to simplify the process and find a NextJS portfolio\n              template tailored to your needs. Imagine having a solution that\n              allows you to jumpstart your digital presence and showcase your\n              skills seamlessly.\n            </p>\n          </div>\n          <div className=\"font-aeonik-bold\">\n            <h2 className=\"font-bold\">Aeonik Bold</h2>\n            <p>\n              Are you looking for inspiration to create a stunning portfolio\n              website that stands out in the given landing page examples?\n              Crafting a captivating online portfolio can be an intimidating\n              task, especially when you're trying to find the right template.\n              There is a way to simplify the process and find a NextJS portfolio\n              template tailored to your needs. Imagine having a solution that\n              allows you to jumpstart your digital presence and showcase your\n              skills seamlessly.\n            </p>\n          </div>\n        </div>\n      </section>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;;0BACd,6WAAC,oIAAQ;;;;;0BACT,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAY;;;;;;8CAC1B,6WAAC;8CAAE;;;;;;;;;;;;sCAWL,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAY;;;;;;8CAC1B,6WAAC;8CAAE;;;;;;;;;;;;sCAWL,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAY;;;;;;8CAC1B,6WAAC;8CAAE;;;;;;;;;;;;sCAWL,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAY;;;;;;8CAC1B,6WAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAef", "debugId": null}}]}