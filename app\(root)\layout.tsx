import BgCover from '@/components/layout/bg-cover';
import FooterC<PERSON> from '@/components/layout/footer-cta';
import SiteFooter from '@/components/layout/site-footer';
import SiteHeader from '@/components/layout/site-header';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="container-wrapper dark relative z-10 flex min-h-svh flex-col bg-my-background text-white">
      <SiteHeader />
      <main className="container relative flex flex-1 flex-col overflow-hidden">
        <BgCover />
        {children}
      </main>
      <FooterCta />
      <SiteFooter />
    </div>
  );
}
