'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { features, navItems } from '@/config/docs';
import { cn } from '@/lib/utils';

export function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {
  const pathname = usePathname();

  return (
    <nav className={cn('items-center gap-0.5', className)} {...props}>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem className="rounded-2xl">
            <NavigationMenuTrigger
              aria-label="Open blog links"
              className="flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50"
            >
              Features
            </NavigationMenuTrigger>
            <NavigationMenuContent className="dark bg-my-background/40 backdrop-blur-lg supports-backdrop-blur:bg-my-background/90">
              <div className="grid w-[500px] p-4 lg:w-[600px]">
                <p className="font-medium text-muted-foreground capitalize tracking-tighter">
                  Features
                </p>
                <div className="grid grid-cols-2 gap-6 py-6">
                  {features.map((feature) => (
                    <NavigationMenuLink
                      asChild
                      className="group rounded-xl p-0 hover:bg-transparent"
                      key={feature.title}
                    >
                      <Link href="/">
                        <div className="flex items-center gap-4">
                          <div className="rounded-lg bg-muted p-3 transition-all duration-300 group-hover:bg-brand-500 dark:group-hover:bg-brand-500">
                            <feature.icon className="block size-5 transition-all duration-300 group-hover:text-white dark:group-hover:text-black" />
                          </div>

                          <div className="flex flex-col gap-1">
                            <div className="font-medium text-md leading-none">
                              {feature.title}
                            </div>
                            <p className="text-muted-foreground text-sm">
                              {feature.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </NavigationMenuLink>
                  ))}
                </div>
              </div>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
      {navItems.map((item) => (
        <Button
          asChild
          className="rounded-full"
          key={item.href}
          size="sm"
          variant="ghost"
        >
          <Link
            className={cn(
              'font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary',
              pathname === item.href && 'text-primary'
            )}
            href={item.href}
          >
            {item.label}
          </Link>
        </Button>
      ))}
    </nav>
  );
}
