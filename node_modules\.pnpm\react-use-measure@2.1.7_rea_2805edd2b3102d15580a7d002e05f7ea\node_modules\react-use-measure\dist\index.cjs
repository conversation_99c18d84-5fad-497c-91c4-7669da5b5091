"use strict";const i=require("react");function b(n,t){let o;return(...s)=>{window.clearTimeout(o),o=window.setTimeout(()=>n(...s),t)}}function S({debounce:n,scroll:t,polyfill:o,offsetSize:s}={debounce:0,scroll:!1,offsetSize:!1}){const a=o||(typeof window=="undefined"?class{}:window.ResizeObserver);if(!a)throw new Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");const[l,h]=i.useState({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),e=i.useRef({element:null,scrollContainers:null,resizeObserver:null,lastBounds:l,orientationHandler:null}),d=n?typeof n=="number"?n:n.scroll:null,f=n?typeof n=="number"?n:n.resize:null,w=i.useRef(!1);i.useEffect(()=>(w.current=!0,()=>void(w.current=!1)));const[p,m,c]=i.useMemo(()=>{const r=()=>{if(!e.current.element)return;const{left:L,top:y,width:C,height:H,bottom:O,right:x,x:R,y:B}=e.current.element.getBoundingClientRect(),u={left:L,top:y,width:C,height:H,bottom:O,right:x,x:R,y:B};e.current.element instanceof HTMLElement&&s&&(u.height=e.current.element.offsetHeight,u.width=e.current.element.offsetWidth),Object.freeze(u),w.current&&!q(e.current.lastBounds,u)&&h(e.current.lastBounds=u)};return[r,f?b(r,f):r,d?b(r,d):r]},[h,s,d,f]);function v(){e.current.scrollContainers&&(e.current.scrollContainers.forEach(r=>r.removeEventListener("scroll",c,!0)),e.current.scrollContainers=null),e.current.resizeObserver&&(e.current.resizeObserver.disconnect(),e.current.resizeObserver=null),e.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",e.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",e.current.orientationHandler))}function E(){e.current.element&&(e.current.resizeObserver=new a(c),e.current.resizeObserver.observe(e.current.element),t&&e.current.scrollContainers&&e.current.scrollContainers.forEach(r=>r.addEventListener("scroll",c,{capture:!0,passive:!0})),e.current.orientationHandler=()=>{c()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",e.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",e.current.orientationHandler))}const z=r=>{!r||r===e.current.element||(v(),e.current.element=r,e.current.scrollContainers=g(r),E())};return D(c,!!t),T(m),i.useEffect(()=>{v(),E()},[t,c,m]),i.useEffect(()=>v,[]),[z,l,p]}function T(n){i.useEffect(()=>{const t=n;return window.addEventListener("resize",t),()=>void window.removeEventListener("resize",t)},[n])}function D(n,t){i.useEffect(()=>{if(t){const o=n;return window.addEventListener("scroll",o,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",o,!0)}},[n,t])}function g(n){const t=[];if(!n||n===document.body)return t;const{overflow:o,overflowX:s,overflowY:a}=window.getComputedStyle(n);return[o,s,a].some(l=>l==="auto"||l==="scroll")&&t.push(n),[...t,...g(n.parentElement)]}const M=["x","y","top","bottom","left","right","width","height"],q=(n,t)=>M.every(o=>n[o]===t[o]);module.exports=S;
//# sourceMappingURL=index.cjs.map
